# GitHub Star 管理器

一个现代化的 GitHub starred 仓库管理工具，帮助您更好地组织和搜索您的 starred 仓库。

## 功能特性

- 🔐 **GitHub OAuth 登录** - 安全的 GitHub 账户授权
- ⭐ **Starred 仓库管理** - 查看和管理所有 starred 仓库
- 🔍 **智能搜索** - 支持仓库名称和描述的模糊搜索
- 🌐 **AI 翻译** - 自动将英文描述翻译为中文
- 📱 **响应式设计** - 完美支持移动端和桌面端
- 🌙 **深色模式** - 支持明暗主题切换
- ⚡ **性能优化** - 分页加载和缓存优化

## 技术栈

- **前端框架**: Next.js 15.3.4
- **UI 框架**: Tailwind CSS 4
- **认证**: NextAuth.js
- **API 集成**: GitHub REST API v3
- **搜索引擎**: Fuse.js
- **翻译服务**: OpenAI API
- **图标**: Lucide React
- **语言**: TypeScript

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

配置 `.env.local` 文件中的环境变量：

```env
# GitHub OAuth 配置
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# OpenAI API (可选，用于翻译功能)
OPENAI_API_KEY=your_openai_api_key
```

### 3. GitHub OAuth 应用设置

1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 创建新的 OAuth App
3. 设置回调 URL: `http://localhost:3000/api/auth/callback/github`

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 开始使用。

## 使用说明

1. **登录**: 使用 GitHub 账户登录
2. **浏览**: 查看所有 starred 仓库列表
3. **搜索**: 使用搜索框查找特定仓库
4. **翻译**: 点击"翻译为中文"按钮获取中文描述
5. **访问**: 点击仓库名称跳转到原仓库

## 部署

推荐使用 Vercel 部署，确保正确配置环境变量。
