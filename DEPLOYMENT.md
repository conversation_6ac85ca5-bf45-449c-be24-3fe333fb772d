# 部署指南

## 环境变量配置

在部署之前，请确保配置以下环境变量：

### 必需的环境变量

```env
# GitHub OAuth 配置
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# NextAuth 配置
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret_here
```

### 可选的环境变量

```env
# OpenAI API (用于翻译功能)
OPENAI_API_KEY=your_openai_api_key
```

## GitHub OAuth 应用配置

1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 点击 "New OAuth App"
3. 填写应用信息：
   - **Application name**: GitHub Star Manager
   - **Homepage URL**: `https://your-domain.com`
   - **Authorization callback URL**: `https://your-domain.com/api/auth/callback/github`
4. 创建应用后，复制 Client ID 和 Client Secret

## Vercel 部署

### 1. 准备代码

```bash
git add .
git commit -m "Ready for deployment"
git push origin main
```

### 2. 在 Vercel 中部署

1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 导入您的 GitHub 仓库
4. 配置环境变量
5. 点击 "Deploy"

### 3. 配置自定义域名（可选）

1. 在 Vercel 项目设置中添加自定义域名
2. 更新 GitHub OAuth 应用的回调 URL
3. 更新 `NEXTAUTH_URL` 环境变量

## 其他平台部署

### Netlify

1. 连接 GitHub 仓库
2. 设置构建命令：`npm run build`
3. 设置发布目录：`.next`
4. 配置环境变量

### Railway

1. 连接 GitHub 仓库
2. 配置环境变量
3. 自动部署

## 部署后检查

1. 访问您的应用 URL
2. 测试 GitHub OAuth 登录
3. 验证 API 端点是否正常工作
4. 检查翻译功能（如果配置了 OpenAI API）

## 故障排除

### OAuth 回调错误

- 检查 GitHub OAuth 应用的回调 URL 是否正确
- 确保 `NEXTAUTH_URL` 环境变量与实际域名匹配

### 翻译功能不工作

- 检查 `OPENAI_API_KEY` 是否正确配置
- 如果没有配置 OpenAI API，翻译功能会返回原文

### 构建失败

- 运行 `npm run build` 本地测试
- 检查所有依赖是否正确安装
- 确保 TypeScript 类型检查通过

## 性能优化建议

1. 启用 Vercel Analytics
2. 配置 CDN 缓存
3. 监控 API 使用情况
4. 考虑添加 Redis 缓存（用于翻译结果）

## 安全注意事项

1. 定期轮换 `NEXTAUTH_SECRET`
2. 监控 GitHub API 使用配额
3. 限制 OpenAI API 使用量
4. 启用 HTTPS（生产环境必需）
