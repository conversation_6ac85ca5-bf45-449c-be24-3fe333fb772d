import { TranslationService } from "@/types";

export class OpenAITranslationService implements TranslationService {
  private apiKey: string;
  private baseURL: string = "https://api.openai.com/v1";

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async translate(text: string, targetLang: string = "zh"): Promise<string> {
    if (!text || text.trim() === "") {
      return "";
    }

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: `你是一个专业的翻译助手。请将以下英文文本翻译成简洁的中文，保持原意的同时使用自然的中文表达。如果是技术相关的描述，请保持技术术语的准确性。只返回翻译结果，不要添加任何解释。`,
            },
            {
              role: "user",
              content: text,
            },
          ],
          max_tokens: 200,
          temperature: 0.3,
        }),
      });

      if (!response.ok) {
        throw new Error(`翻译请求失败: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content?.trim() || text;
    } catch (error) {
      console.error("翻译失败:", error);
      return text; // 翻译失败时返回原文
    }
  }
}

// 简单的缓存翻译服务
export class CachedTranslationService implements TranslationService {
  private cache = new Map<string, string>();
  private translationService: TranslationService;

  constructor(translationService: TranslationService) {
    this.translationService = translationService;
  }

  async translate(text: string, targetLang?: string): Promise<string> {
    const cacheKey = `${text}_${targetLang || "zh"}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const result = await this.translationService.translate(text, targetLang);
    this.cache.set(cacheKey, result);
    
    return result;
  }

  clearCache() {
    this.cache.clear();
  }
}

// 批量翻译函数
export async function batchTranslate(
  texts: string[],
  translationService: TranslationService,
  batchSize: number = 5
): Promise<string[]> {
  const results: string[] = [];
  
  for (let i = 0; i < texts.length; i += batchSize) {
    const batch = texts.slice(i, i + batchSize);
    const batchPromises = batch.map(text => translationService.translate(text));
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // 添加延迟以避免 API 限制
    if (i + batchSize < texts.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}
