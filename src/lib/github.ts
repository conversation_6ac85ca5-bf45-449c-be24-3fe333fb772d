import { Octokit } from "@octokit/rest";
import { GitHubRepository, GitHub<PERSON>ser, StarredRepositoriesResponse } from "@/types";

export class GitHubService {
  private octokit: Octokit;

  constructor(accessToken: string) {
    this.octokit = new Octokit({
      auth: accessToken,
    });
  }

  async getUser(): Promise<GitHubUser> {
    const { data } = await this.octokit.rest.users.getAuthenticated();
    return {
      login: data.login,
      id: data.id,
      avatar_url: data.avatar_url,
      name: data.name,
      email: data.email,
      public_repos: data.public_repos,
      followers: data.followers,
      following: data.following,
    };
  }

  async getStarredRepositories(
    page: number = 1,
    perPage: number = 30
  ): Promise<StarredRepositoriesResponse> {
    try {
      const { data, headers } = await this.octokit.rest.activity.listReposStarredByAuthenticatedUser({
        page,
        per_page: perPage,
        sort: "created",
        direction: "desc",
      });

      // 解析 Link header 来确定是否有下一页
      const linkHeader = headers.link;
      const hasNextPage = linkHeader ? linkHeader.includes('rel="next"') : false;

      const repositories: GitHubRepository[] = data.map((repo) => ({
        id: repo.id,
        name: repo.name,
        full_name: repo.full_name,
        description: repo.description,
        html_url: repo.html_url,
        stargazers_count: repo.stargazers_count,
        language: repo.language,
        topics: repo.topics || [],
        created_at: repo.created_at,
        updated_at: repo.updated_at,
        pushed_at: repo.pushed_at,
        owner: {
          login: repo.owner.login,
          avatar_url: repo.owner.avatar_url,
          html_url: repo.owner.html_url,
        },
      }));

      return {
        repositories,
        hasNextPage,
        endCursor: hasNextPage ? String(page + 1) : null,
        totalCount: repositories.length, // GitHub API 不直接提供总数
      };
    } catch (error) {
      console.error("获取 starred repositories 失败:", error);
      throw new Error("无法获取 starred repositories");
    }
  }

  async getAllStarredRepositories(): Promise<GitHubRepository[]> {
    const allRepos: GitHubRepository[] = [];
    let page = 1;
    let hasNextPage = true;

    while (hasNextPage) {
      const response = await this.getStarredRepositories(page, 100);
      allRepos.push(...response.repositories);
      hasNextPage = response.hasNextPage;
      page++;

      // 防止无限循环，最多获取 1000 个仓库
      if (page > 10) {
        break;
      }
    }

    return allRepos;
  }
}
