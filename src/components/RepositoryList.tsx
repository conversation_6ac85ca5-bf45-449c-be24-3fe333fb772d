"use client";

import { EnhancedRepository } from "@/types";
import { RepositoryCard } from "./RepositoryCard";
import { LoadingSpinner } from "./LoadingSpinner";
import { ChevronDown } from "lucide-react";

interface RepositoryListProps {
  repositories: EnhancedRepository[];
  onTranslate: (repo: EnhancedRepository) => void;
  onLoadMore?: () => void;
  hasNextPage?: boolean;
  isLoading?: boolean;
}

export function RepositoryList({
  repositories,
  onTranslate,
  onLoadMore,
  hasNextPage = false,
  isLoading = false,
}: RepositoryListProps) {
  if (repositories.length === 0 && !isLoading) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 dark:text-gray-400">
          <p className="text-lg mb-2">没有找到仓库</p>
          <p className="text-sm">尝试调整搜索条件或检查网络连接</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 仓库列表 */}
      <div className="grid gap-4 md:gap-6">
        {repositories.map((repo) => (
          <RepositoryCard
            key={repo.id}
            repository={repo}
            onTranslate={onTranslate}
          />
        ))}
      </div>

      {/* 加载更多按钮 */}
      {hasNextPage && (
        <div className="flex justify-center pt-8">
          <button
            onClick={onLoadMore}
            disabled={isLoading}
            className="flex items-center space-x-2 px-6 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
            <span>{isLoading ? "加载中..." : "加载更多"}</span>
          </button>
        </div>
      )}

      {/* 底部加载状态 */}
      {isLoading && repositories.length > 0 && (
        <div className="flex justify-center py-4">
          <LoadingSpinner size="sm" text="正在加载更多仓库..." />
        </div>
      )}
    </div>
  );
}
