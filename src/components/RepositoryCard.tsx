"use client";

import { EnhancedRepository } from "@/types";
import { Star, ExternalLink, Calendar, Languages, Tag, Loader2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";

interface RepositoryCardProps {
  repository: EnhancedRepository;
  onTranslate: (repo: EnhancedRepository) => void;
}

export function RepositoryCard({ repository, onTranslate }: RepositoryCardProps) {
  const {
    name,
    full_name,
    description,
    description_zh,
    html_url,
    stargazers_count,
    language,
    topics,
    updated_at,
    owner,
    isTranslating,
    translationError,
  } = repository;

  const handleTranslate = () => {
    if (!isTranslating && !description_zh && description) {
      onTranslate(repository);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const getLanguageColor = (lang: string | null) => {
    const colors: Record<string, string> = {
      JavaScript: "bg-yellow-400",
      TypeScript: "bg-blue-500",
      Python: "bg-green-500",
      Java: "bg-red-500",
      "C++": "bg-pink-500",
      C: "bg-gray-600",
      Go: "bg-cyan-500",
      Rust: "bg-orange-600",
      PHP: "bg-purple-500",
      Ruby: "bg-red-600",
      Swift: "bg-orange-500",
      Kotlin: "bg-purple-600",
    };
    return colors[lang || ""] || "bg-gray-400";
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-lg transition-shadow">
      {/* 头部信息 */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <img
              src={owner.avatar_url}
              alt={owner.login}
              className="w-5 h-5 rounded-full"
            />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
              <a
                href={html_url}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                {full_name}
              </a>
            </h3>
            <ExternalLink className="h-4 w-4 text-gray-400 flex-shrink-0" />
          </div>
          
          {/* Star 数量和语言 */}
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Star className="h-4 w-4 text-yellow-500" />
              <span>{formatNumber(stargazers_count)}</span>
            </div>
            
            {language && (
              <div className="flex items-center space-x-1">
                <div className={`w-3 h-3 rounded-full ${getLanguageColor(language)}`} />
                <span>{language}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>
                {formatDistanceToNow(new Date(updated_at), {
                  addSuffix: true,
                  locale: zhCN,
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 描述 */}
      {description && (
        <div className="mb-4">
          <p className="text-gray-700 dark:text-gray-300 text-sm mb-2 leading-relaxed">
            {description}
          </p>
          
          {/* 中文翻译 */}
          {description_zh && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
              <p className="text-blue-800 dark:text-blue-200 text-sm leading-relaxed">
                🇨🇳 {description_zh}
              </p>
            </div>
          )}
          
          {/* 翻译按钮/状态 */}
          {!description_zh && !translationError && (
            <button
              onClick={handleTranslate}
              disabled={isTranslating}
              className="inline-flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors disabled:opacity-50"
            >
              {isTranslating ? (
                <>
                  <Loader2 className="h-3 w-3 animate-spin" />
                  <span>翻译中...</span>
                </>
              ) : (
                <>
                  <Languages className="h-3 w-3" />
                  <span>翻译为中文</span>
                </>
              )}
            </button>
          )}
          
          {/* 翻译错误 */}
          {translationError && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-1">
              {translationError}
            </p>
          )}
        </div>
      )}

      {/* 标签 */}
      {topics && topics.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {topics.slice(0, 6).map((topic) => (
            <span
              key={topic}
              className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            >
              <Tag className="h-3 w-3 mr-1" />
              {topic}
            </span>
          ))}
          {topics.length > 6 && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              +{topics.length - 6} 更多
            </span>
          )}
        </div>
      )}
    </div>
  );
}
