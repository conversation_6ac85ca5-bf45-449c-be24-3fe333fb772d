"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { Star, LogOut, User, RefreshCw } from "lucide-react";
import { EnhancedRepository } from "@/types";
import { RepositoryList } from "@/components/RepositoryList";
import { SearchBar } from "@/components/SearchBar";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import Fuse from "fuse.js";

interface DevUser {
  id: number;
  name: string;
  email: string;
  login: string;
  avatar_url: string;
}

interface DevSession {
  user: DevUser | null;
  accessToken: string | null;
  isDev?: boolean;
}

export default function DevApp() {
  const [session, setSession] = useState<DevSession>({ user: null, accessToken: null });
  const [repositories, setRepositories] = useState<EnhancedRepository[]>([]);
  const [filteredRepositories, setFilteredRepositories] = useState<EnhancedRepository[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 配置 Fuse.js 搜索选项
  const fuse = useMemo(() => {
    const fuseOptions = {
      keys: [
        { name: "name", weight: 0.3 },
        { name: "full_name", weight: 0.3 },
        { name: "description", weight: 0.2 },
        { name: "description_zh", weight: 0.2 },
      ],
      threshold: 0.4,
      includeScore: true,
      includeMatches: true,
    };
    return new Fuse(repositories, fuseOptions);
  }, [repositories]);

  // 检查登录状态
  const checkSession = useCallback(async () => {
    try {
      const response = await fetch("/api/auth/session-simple");
      const data = await response.json();
      setSession(data);
      return data.user !== null;
    } catch (error) {
      console.error("检查 session 失败:", error);
      return false;
    }
  }, []);

  // 获取仓库列表
  const fetchRepositories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/github/starred-dev?page=1&per_page=30");
      if (!response.ok) {
        throw new Error("获取仓库列表失败");
      }

      const data = await response.json();
      setRepositories(data.repositories);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取数据失败");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // 翻译仓库描述
  const translateDescription = useCallback(async (repo: EnhancedRepository) => {
    if (!repo.description || repo.description_zh || repo.isTranslating) {
      return;
    }

    setRepositories(prev =>
      prev.map(r =>
        r.id === repo.id ? { ...r, isTranslating: true } : r
      )
    );

    try {
      const response = await fetch("/api/translate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text: repo.description }),
      });

      if (response.ok) {
        const { translatedText } = await response.json();
        setRepositories(prev =>
          prev.map(r =>
            r.id === repo.id
              ? { ...r, description_zh: translatedText, isTranslating: false }
              : r
          )
        );
      }
    } catch {
      setRepositories(prev =>
        prev.map(r =>
          r.id === repo.id
            ? { ...r, isTranslating: false, translationError: "翻译失败" }
            : r
        )
      );
    }
  }, []);

  // 搜索功能
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredRepositories(repositories);
      return;
    }

    const results = fuse.search(searchQuery);
    setFilteredRepositories(results.map(result => result.item));
  }, [searchQuery, repositories, fuse]);

  // 初始化
  useEffect(() => {
    const init = async () => {
      const isLoggedIn = await checkSession();
      if (isLoggedIn) {
        await fetchRepositories();
      } else {
        setIsLoading(false);
      }
    };
    init();
  }, [checkSession, fetchRepositories]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchRepositories();
  };

  const handleSignOut = async () => {
    try {
      await fetch("/api/auth/session-simple", { method: "DELETE" });
      setSession({ user: null, accessToken: null });
      setRepositories([]);
      setFilteredRepositories([]);
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  // 如果未登录，重定向到开发登录页面
  if (!session.user && !isLoading) {
    window.location.href = "/test-dev-auth";
    return null;
  }

  if (isLoading && repositories.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="正在加载您的 starred 仓库..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 头部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14 sm:h-16">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0">
              <Star className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500 flex-shrink-0" />
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white truncate">
                <span className="hidden sm:inline">GitHub Star 管理器</span>
                <span className="sm:hidden">Star 管理器</span>
                <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded ml-2">
                  开发模式
                </span>
              </h1>
            </div>

            <div className="flex items-center space-x-2 sm:space-x-4">
              {session.user && (
                <div className="hidden md:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <User className="h-4 w-4" />
                  <span className="truncate max-w-24 lg:max-w-none">{session.user.name}</span>
                </div>
              )}
              
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="刷新"
              >
                <RefreshCw className={`h-4 w-4 sm:h-5 sm:w-5 ${isRefreshing ? "animate-spin" : ""}`} />
              </button>

              <button
                onClick={handleSignOut}
                className="p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="退出登录"
              >
                <LogOut className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* 开发模式提示 */}
        <div className="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <p className="text-yellow-800 text-sm">
            ⚠️ 当前运行在开发模式下，使用模拟数据。网络问题解决后可切换到真实的 GitHub API。
          </p>
        </div>

        {/* 搜索栏 */}
        <div className="mb-6 sm:mb-8">
          <SearchBar
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="搜索仓库名称或描述..."
            className="w-full"
          />
        </div>

        {/* 统计信息 */}
        <div className="mb-4 sm:mb-6 text-xs sm:text-sm text-gray-600 dark:text-gray-400 px-1">
          显示 <span className="font-medium">{filteredRepositories.length}</span> 个仓库
          {searchQuery && (
            <span className="hidden sm:inline">
              {" "}(从 {repositories.length} 个仓库中搜索)
            </span>
          )}
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* 仓库列表 */}
        <RepositoryList
          repositories={filteredRepositories}
          onTranslate={translateDescription}
          onLoadMore={() => {}} // 开发模式不需要加载更多
          hasNextPage={false}
          isLoading={isLoading}
        />
      </main>
    </div>
  );
}
