"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { useState } from "react";

export default function TestAuth() {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    setIsLoading(true);
    try {
      const result = await signIn("github", { 
        callbackUrl: "/test-auth",
        redirect: false 
      });
      console.log("SignIn result:", result);
    } catch (error) {
      console.error("SignIn error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/test-auth" });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          GitHub OAuth 测试
        </h1>

        <div className="space-y-4">
          <div className="text-sm">
            <strong>状态:</strong> {status}
          </div>

          {status === "loading" && (
            <div className="text-center text-gray-600">
              正在检查登录状态...
            </div>
          )}

          {status === "unauthenticated" && (
            <div className="space-y-4">
              <p className="text-gray-600 text-center">
                您尚未登录
              </p>
              <button
                onClick={handleSignIn}
                disabled={isLoading}
                className="w-full bg-gray-800 text-white py-2 px-4 rounded hover:bg-gray-700 disabled:opacity-50"
              >
                {isLoading ? "登录中..." : "使用 GitHub 登录"}
              </button>
            </div>
          )}

          {status === "authenticated" && session && (
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-green-600 font-medium">
                  ✅ 登录成功！
                </p>
                <div className="mt-2 text-sm text-gray-600">
                  <p><strong>用户:</strong> {session.user?.name || session.user?.email}</p>
                  <p><strong>邮箱:</strong> {session.user?.email}</p>
                  {session.accessToken && (
                    <p><strong>Access Token:</strong> {session.accessToken.substring(0, 20)}...</p>
                  )}
                </div>
              </div>
              <button
                onClick={handleSignOut}
                className="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700"
              >
                退出登录
              </button>
            </div>
          )}

          <div className="mt-6 text-xs text-gray-500">
            <p>这是一个测试页面，用于验证 GitHub OAuth 配置。</p>
            <p>如果登录成功，您可以返回主应用。</p>
          </div>
        </div>
      </div>
    </div>
  );
}
