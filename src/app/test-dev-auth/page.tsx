"use client";

import { useState, useEffect } from "react";

interface User {
  id: number;
  name: string;
  email: string;
  login: string;
  avatar_url: string;
}

interface Session {
  user: User | null;
  accessToken: string | null;
  isDev?: boolean;
}

export default function TestDevAuth() {
  const [session, setSession] = useState<Session>({ user: null, accessToken: null });
  const [isLoading, setIsLoading] = useState(true);
  const [username, setUsername] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const response = await fetch("/api/auth/session-simple");
      const data = await response.json();
      setSession(data);
    } catch (error) {
      console.error("检查 session 失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDevLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim()) return;

    setIsLoggingIn(true);
    try {
      const response = await fetch("/api/auth/dev-login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username: username.trim() }),
      });

      if (response.ok) {
        await checkSession(); // 重新检查 session
      } else {
        const error = await response.json();
        alert(`登录失败: ${error.error}`);
      }
    } catch (error) {
      console.error("登录失败:", error);
      alert("登录失败，请重试");
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await fetch("/api/auth/session-simple", { method: "DELETE" });
      setSession({ user: null, accessToken: null });
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">检查登录状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          开发环境认证测试
        </h1>

        <div className="space-y-4">
          {!session.user ? (
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-sm">
                  ⚠️ 由于网络连接问题，GitHub OAuth 暂时不可用。
                  <br />
                  使用开发模式进行测试。
                </p>
              </div>

              <form onSubmit={handleDevLogin} className="space-y-4">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                    模拟用户名
                  </label>
                  <input
                    type="text"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="输入任意用户名"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <button
                  type="submit"
                  disabled={isLoggingIn || !username.trim()}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoggingIn ? "登录中..." : "开发模式登录"}
                </button>
              </form>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <img
                    src={session.user.avatar_url}
                    alt={session.user.login}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-gray-900">
                      {session.user.name}
                    </p>
                    <p className="text-sm text-gray-600">
                      @{session.user.login}
                    </p>
                  </div>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                  <p className="text-green-800 font-medium">
                    ✅ 登录成功！
                    {session.isDev && (
                      <span className="block text-sm font-normal">
                        (开发模式)
                      </span>
                    )}
                  </p>
                </div>

                <div className="text-left text-sm text-gray-600 space-y-1">
                  <p><strong>用户 ID:</strong> {session.user.id}</p>
                  <p><strong>邮箱:</strong> {session.user.email}</p>
                  <p><strong>Access Token:</strong> {session.accessToken?.substring(0, 20)}...</p>
                </div>
              </div>
              
              <button
                onClick={handleSignOut}
                className="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700 transition-colors"
              >
                退出登录
              </button>
            </div>
          )}

          <div className="mt-6 text-xs text-gray-500">
            <p>这是开发环境的认证测试。</p>
            <p>在网络问题解决后，可以切换回真实的 GitHub OAuth。</p>
          </div>
        </div>
      </div>
    </div>
  );
}
