"use client";

import { useState, useEffect } from "react";

interface User {
  id: number;
  name: string;
  email: string;
  login: string;
  avatar_url: string;
}

interface Session {
  user: User | null;
  accessToken: string | null;
}

export default function TestSimpleAuth() {
  const [session, setSession] = useState<Session>({ user: null, accessToken: null });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const response = await fetch("/api/auth/session-simple");
      const data = await response.json();
      setSession(data);
    } catch (error) {
      console.error("检查 session 失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = () => {
    window.location.href = "/api/auth/github";
  };

  const handleSignOut = async () => {
    try {
      await fetch("/api/auth/session-simple", { method: "DELETE" });
      setSession({ user: null, accessToken: null });
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">检查登录状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          简化版 GitHub OAuth 测试
        </h1>

        <div className="space-y-4">
          {!session.user ? (
            <div className="space-y-4">
              <p className="text-gray-600 text-center">
                您尚未登录
              </p>
              <button
                onClick={handleSignIn}
                className="w-full bg-gray-800 text-white py-2 px-4 rounded hover:bg-gray-700 transition-colors"
              >
                使用 GitHub 登录 (简化版)
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <img
                    src={session.user.avatar_url}
                    alt={session.user.login}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-gray-900">
                      {session.user.name || session.user.login}
                    </p>
                    <p className="text-sm text-gray-600">
                      @{session.user.login}
                    </p>
                  </div>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                  <p className="text-green-800 font-medium">
                    ✅ 登录成功！
                  </p>
                </div>

                <div className="text-left text-sm text-gray-600 space-y-1">
                  <p><strong>用户 ID:</strong> {session.user.id}</p>
                  <p><strong>邮箱:</strong> {session.user.email || "未公开"}</p>
                  <p><strong>Access Token:</strong> {session.accessToken?.substring(0, 20)}...</p>
                </div>
              </div>
              
              <button
                onClick={handleSignOut}
                className="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700 transition-colors"
              >
                退出登录
              </button>
            </div>
          )}

          <div className="mt-6 text-xs text-gray-500">
            <p>这是一个简化版的 GitHub OAuth 实现，绕过了 NextAuth.js 的超时问题。</p>
            <p>如果这个版本能正常工作，我们就可以集成到主应用中。</p>
          </div>
        </div>
      </div>
    </div>
  );
}
