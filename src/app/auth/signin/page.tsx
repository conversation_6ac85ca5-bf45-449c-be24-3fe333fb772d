"use client";

import { signIn, getSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Github, Star } from "lucide-react";

export default function SignIn() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已经登录
    getSession().then((session) => {
      if (session) {
        router.push("/");
      }
    });
  }, [router]);

  const handleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("github", { callbackUrl: "/" });
    } catch (error) {
      console.error("登录失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-6 sm:space-y-8 p-6 sm:p-8">
        <div className="text-center">
          <div className="flex justify-center items-center space-x-2 mb-4">
            <Star className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              <span className="hidden sm:inline">GitHub Star 管理器</span>
              <span className="sm:hidden">Star 管理器</span>
            </h1>
          </div>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
            管理和搜索您的 GitHub starred 仓库
          </p>
        </div>

        <div className="mt-6 sm:mt-8 space-y-4 sm:space-y-6">
          <button
            onClick={handleSignIn}
            disabled={isLoading}
            className="group relative w-full flex justify-center py-2.5 sm:py-3 px-4 border border-transparent text-sm sm:text-base font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span className="absolute left-0 inset-y-0 flex items-center pl-3">
              <Github className="h-4 w-4 sm:h-5 sm:w-5 text-gray-300 group-hover:text-gray-200" />
            </span>
            {isLoading ? "登录中..." : "使用 GitHub 登录"}
          </button>

          <div className="text-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
            <p className="mb-2">登录后您可以：</p>
            <ul className="space-y-1 text-left max-w-xs mx-auto">
              <li className="flex items-center space-x-2">
                <span className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
                <span>查看所有 starred 仓库</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
                <span>搜索仓库描述</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
                <span>查看中文翻译描述</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
