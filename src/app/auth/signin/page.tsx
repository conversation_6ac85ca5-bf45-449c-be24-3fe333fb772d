"use client";

import { signIn, getSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Github, Star } from "lucide-react";

export default function SignIn() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // 检查用户是否已经登录
    getSession().then((session) => {
      if (session) {
        router.push("/");
      }
    });
  }, [router]);

  const handleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("github", { callbackUrl: "/" });
    } catch (error) {
      console.error("登录失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="flex justify-center items-center space-x-2 mb-4">
            <Star className="h-8 w-8 text-yellow-500" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              GitHub Star 管理器
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            管理和搜索您的 GitHub starred 仓库
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <button
            onClick={handleSignIn}
            disabled={isLoading}
            className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span className="absolute left-0 inset-y-0 flex items-center pl-3">
              <Github className="h-5 w-5 text-gray-300 group-hover:text-gray-200" />
            </span>
            {isLoading ? "登录中..." : "使用 GitHub 登录"}
          </button>

          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p>登录后您可以：</p>
            <ul className="mt-2 space-y-1">
              <li>• 查看所有 starred 仓库</li>
              <li>• 搜索仓库描述</li>
              <li>• 查看中文翻译描述</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
