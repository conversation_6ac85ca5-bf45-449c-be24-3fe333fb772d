import { NextRequest, NextResponse } from "next/server";
import { OpenAITranslationService, CachedTranslationService } from "@/lib/translation";

export async function POST(request: NextRequest) {
  try {
    const { text, targetLang = "zh" } = await request.json();

    if (!text || typeof text !== "string") {
      return NextResponse.json(
        { error: "缺少有效的文本参数" },
        { status: 400 }
      );
    }

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      // 如果没有配置 OpenAI API Key，返回原文
      return NextResponse.json({ 
        translatedText: text,
        message: "翻译服务未配置，返回原文"
      });
    }

    const openAIService = new OpenAITranslationService(apiKey);
    const cachedService = new CachedTranslationService(openAIService);
    
    const translatedText = await cachedService.translate(text, targetLang);

    return NextResponse.json({ translatedText });
  } catch (error) {
    console.error("翻译失败:", error);
    return NextResponse.json(
      { error: "翻译服务暂时不可用" },
      { status: 500 }
    );
  }
}
