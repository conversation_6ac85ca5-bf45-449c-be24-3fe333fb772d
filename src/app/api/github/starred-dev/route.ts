import { NextRequest, NextResponse } from "next/server";

// 模拟的 starred repositories 数据
const mockRepositories = [
  {
    id: 1,
    name: "react",
    full_name: "facebook/react",
    description: "A declarative, efficient, and flexible JavaScript library for building user interfaces.",
    html_url: "https://github.com/facebook/react",
    stargazers_count: 220000,
    language: "JavaScript",
    topics: ["javascript", "react", "frontend", "ui"],
    created_at: "2013-05-24T16:15:54Z",
    updated_at: "2024-01-15T10:30:00Z",
    pushed_at: "2024-01-15T09:45:00Z",
    owner: {
      login: "facebook",
      avatar_url: "https://avatars.githubusercontent.com/u/69631?v=4",
      html_url: "https://github.com/facebook",
    },
  },
  {
    id: 2,
    name: "vue",
    full_name: "vuejs/vue",
    description: "🖖 Vue.js is a progressive, incrementally-adoptable JavaScript framework for building UI on the web.",
    html_url: "https://github.com/vuejs/vue",
    stargazers_count: 207000,
    language: "TypeScript",
    topics: ["vue", "javascript", "frontend", "framework"],
    created_at: "2013-07-29T03:24:51Z",
    updated_at: "2024-01-14T15:20:00Z",
    pushed_at: "2024-01-14T14:30:00Z",
    owner: {
      login: "vuejs",
      avatar_url: "https://avatars.githubusercontent.com/u/6128107?v=4",
      html_url: "https://github.com/vuejs",
    },
  },
  {
    id: 3,
    name: "next.js",
    full_name: "vercel/next.js",
    description: "The React Framework for the Web",
    html_url: "https://github.com/vercel/next.js",
    stargazers_count: 120000,
    language: "JavaScript",
    topics: ["react", "nextjs", "vercel", "framework", "ssr"],
    created_at: "2016-10-05T00:57:05Z",
    updated_at: "2024-01-15T08:15:00Z",
    pushed_at: "2024-01-15T07:45:00Z",
    owner: {
      login: "vercel",
      avatar_url: "https://avatars.githubusercontent.com/u/14985020?v=4",
      html_url: "https://github.com/vercel",
    },
  },
  {
    id: 4,
    name: "tailwindcss",
    full_name: "tailwindlabs/tailwindcss",
    description: "A utility-first CSS framework for rapid UI development.",
    html_url: "https://github.com/tailwindlabs/tailwindcss",
    stargazers_count: 78000,
    language: "JavaScript",
    topics: ["css", "tailwind", "utility-first", "design-system"],
    created_at: "2017-06-01T14:22:53Z",
    updated_at: "2024-01-14T20:10:00Z",
    pushed_at: "2024-01-14T19:30:00Z",
    owner: {
      login: "tailwindlabs",
      avatar_url: "https://avatars.githubusercontent.com/u/67109815?v=4",
      html_url: "https://github.com/tailwindlabs",
    },
  },
  {
    id: 5,
    name: "typescript",
    full_name: "microsoft/TypeScript",
    description: "TypeScript is a superset of JavaScript that compiles to clean JavaScript output.",
    html_url: "https://github.com/microsoft/TypeScript",
    stargazers_count: 98000,
    language: "TypeScript",
    topics: ["typescript", "javascript", "compiler", "language"],
    created_at: "2012-10-01T15:49:56Z",
    updated_at: "2024-01-15T11:00:00Z",
    pushed_at: "2024-01-15T10:15:00Z",
    owner: {
      login: "microsoft",
      avatar_url: "https://avatars.githubusercontent.com/u/6154722?v=4",
      html_url: "https://github.com/microsoft",
    },
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "30");

    // 模拟分页
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedRepos = mockRepositories.slice(startIndex, endIndex);

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return NextResponse.json({
      repositories: paginatedRepos,
      hasNextPage: endIndex < mockRepositories.length,
      endCursor: endIndex < mockRepositories.length ? String(page + 1) : null,
      totalCount: mockRepositories.length,
    });
  } catch (error) {
    console.error("获取模拟 starred repositories 失败:", error);
    return NextResponse.json(
      { error: "获取数据失败" },
      { status: 500 }
    );
  }
}
