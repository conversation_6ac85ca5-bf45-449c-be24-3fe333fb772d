import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const state = searchParams.get("state");

  if (!code) {
    // 重定向到 GitHub OAuth
    const githubAuthUrl = new URL("https://github.com/login/oauth/authorize");
    githubAuthUrl.searchParams.set("client_id", process.env.GITHUB_CLIENT_ID!);
    githubAuthUrl.searchParams.set("redirect_uri", `http://localhost:3000/api/auth/callback/github-simple`);
    githubAuthUrl.searchParams.set("scope", "read:user user:email");
    githubAuthUrl.searchParams.set("state", "github-auth");

    return NextResponse.redirect(githubAuthUrl.toString());
  }

  try {
    // 交换访问令牌
    const tokenResponse = await fetch("https://github.com/login/oauth/access_token", {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: process.env.GITHUB_CLIENT_ID!,
        client_secret: process.env.GITHUB_CLIENT_SECRET!,
        code,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (tokenData.error) {
      throw new Error(tokenData.error_description || tokenData.error);
    }

    // 获取用户信息
    const userResponse = await fetch("https://api.github.com/user", {
      headers: {
        "Authorization": `Bearer ${tokenData.access_token}`,
        "Accept": "application/vnd.github.v3+json",
      },
    });

    const userData = await userResponse.json();

    // 创建简单的 session (在实际应用中应该使用更安全的方式)
    const sessionData = {
      user: {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        login: userData.login,
        avatar_url: userData.avatar_url,
      },
      accessToken: tokenData.access_token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后过期
    };

    // 设置 cookie 并重定向
    const response = NextResponse.redirect(`${process.env.NEXTAUTH_URL}/test-auth`);
    response.cookies.set("github-session", JSON.stringify(sessionData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 24 * 60 * 60, // 24小时
    });

    return response;
  } catch (error) {
    console.error("GitHub OAuth error:", error);
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/auth/signin?error=OAuthError`);
  }
}
