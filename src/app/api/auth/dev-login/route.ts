import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json();

    if (!username) {
      return NextResponse.json({ error: "用户名不能为空" }, { status: 400 });
    }

    // 开发环境模拟用户数据
    const mockUserData = {
      id: Math.floor(Math.random() * 1000000),
      name: username,
      email: `${username}@example.com`,
      login: username,
      avatar_url: `https://github.com/identicons/${username}.png`,
    };

    // 模拟访问令牌
    const mockAccessToken = `gho_dev_${Buffer.from(username).toString('base64')}_${Date.now()}`;

    // 创建 session
    const sessionData = {
      user: mockUserData,
      accessToken: mockAccessToken,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      isDev: true, // 标记为开发模式
    };

    // 设置 cookie
    const response = NextResponse.json({ success: true, user: mockUserData });
    response.cookies.set("github-session", JSON.stringify(sessionData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 24 * 60 * 60,
    });

    return response;
  } catch (error) {
    console.error("开发登录错误:", error);
    return NextResponse.json({ error: "登录失败" }, { status: 500 });
  }
}
