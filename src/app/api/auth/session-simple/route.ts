import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get("github-session");
    
    if (!sessionCookie) {
      return NextResponse.json({ user: null, accessToken: null });
    }

    const sessionData = JSON.parse(sessionCookie.value);
    
    // 检查是否过期
    if (new Date(sessionData.expires) < new Date()) {
      // 清除过期的 cookie
      const response = NextResponse.json({ user: null, accessToken: null });
      response.cookies.delete("github-session");
      return response;
    }

    return NextResponse.json({
      user: sessionData.user,
      accessToken: sessionData.accessToken,
    });
  } catch (error) {
    console.error("Session error:", error);
    return NextResponse.json({ user: null, accessToken: null });
  }
}

export async function DELETE() {
  // 登出
  const response = NextResponse.json({ success: true });
  response.cookies.delete("github-session");
  return response;
}
