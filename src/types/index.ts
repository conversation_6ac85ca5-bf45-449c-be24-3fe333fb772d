// GitHub API 相关类型
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  stargazers_count: number;
  language: string | null;
  topics: string[];
  created_at: string;
  updated_at: string;
  pushed_at: string;
  owner: {
    login: string;
    avatar_url: string;
    html_url: string;
  };
}

// 扩展的仓库类型，包含翻译后的描述
export interface EnhancedRepository extends GitHubRepository {
  description_zh?: string; // 中文翻译描述
  isTranslating?: boolean; // 是否正在翻译
  translationError?: string; // 翻译错误信息
}

// 用户信息类型
export interface GitHubUser {
  login: string;
  id: number;
  avatar_url: string;
  name: string | null;
  email: string | null;
  public_repos: number;
  followers: number;
  following: number;
}

// API 响应类型
export interface StarredRepositoriesResponse {
  repositories: GitHubRepository[];
  hasNextPage: boolean;
  endCursor: string | null;
  totalCount: number;
}

// 搜索相关类型
export interface SearchResult {
  item: EnhancedRepository;
  score: number;
  matches: Array<{
    indices: [number, number][];
    value: string;
    key: string;
  }>;
}

// 分页参数
export interface PaginationParams {
  page: number;
  per_page: number;
  cursor?: string;
}

// API 错误类型
export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

// 翻译服务类型
export interface TranslationService {
  translate(text: string, targetLang?: string): Promise<string>;
}

// 应用状态类型
export interface AppState {
  user: GitHubUser | null;
  repositories: EnhancedRepository[];
  filteredRepositories: EnhancedRepository[];
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  hasNextPage: boolean;
  currentPage: number;
}
