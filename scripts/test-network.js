#!/usr/bin/env node

/**
 * 网络连接诊断脚本
 */

const https = require('https');
const { exec } = require('child_process');

console.log('🌐 网络连接诊断工具\n');

// 测试 DNS 解析
function testDNS() {
  return new Promise((resolve) => {
    exec('nslookup github.com', (error, stdout, stderr) => {
      if (error) {
        console.log('❌ DNS 解析失败:', error.message);
        resolve(false);
      } else {
        console.log('✅ DNS 解析正常');
        resolve(true);
      }
    });
  });
}

// 测试 HTTPS 连接
function testHTTPS(hostname, timeout = 10000) {
  return new Promise((resolve) => {
    const options = {
      hostname,
      port: 443,
      path: '/',
      method: 'GET',
      timeout,
      headers: {
        'User-Agent': 'GitHub-Star-Manager-Test'
      }
    };

    const req = https.request(options, (res) => {
      console.log(`✅ HTTPS 连接到 ${hostname} 成功 (状态码: ${res.statusCode})`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ HTTPS 连接到 ${hostname} 失败:`, err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`❌ HTTPS 连接到 ${hostname} 超时`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// 测试 GitHub API
function testGitHubAPI() {
  return new Promise((resolve) => {
    const options = {
      hostname: 'api.github.com',
      port: 443,
      path: '/user',
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'GitHub-Star-Manager-Test',
        'Accept': 'application/vnd.github.v3+json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`✅ GitHub API 连接成功 (状态码: ${res.statusCode})`);
        resolve(true);
      });
    });

    req.on('error', (err) => {
      console.log('❌ GitHub API 连接失败:', err.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ GitHub API 连接超时');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function main() {
  console.log('1. 测试 DNS 解析:');
  await testDNS();

  console.log('\n2. 测试 HTTPS 连接:');
  await testHTTPS('github.com');
  await testHTTPS('api.github.com');

  console.log('\n3. 测试 GitHub API:');
  await testGitHubAPI();

  console.log('\n4. 网络环境检查:');
  console.log('   如果上述测试失败，可能的原因:');
  console.log('   - 防火墙阻止了 HTTPS 连接');
  console.log('   - 公司网络需要代理设置');
  console.log('   - DNS 服务器配置问题');
  console.log('   - 网络连接不稳定');
  
  console.log('\n5. 建议的解决方案:');
  console.log('   - 检查防火墙设置');
  console.log('   - 尝试使用不同的网络连接');
  console.log('   - 配置 HTTP 代理（如果需要）');
  console.log('   - 联系网络管理员');
}

main().catch(console.error);
