#!/usr/bin/env node

/**
 * 简单的 API 测试脚本
 * 用于验证应用程序的基本功能
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const req = http.get(`${BASE_URL}${path}`, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testEndpoints() {
  const endpoints = [
    '/',
    '/auth/signin',
    '/api/auth/session',
    '/api/auth/providers'
  ];

  console.log('🧪 开始测试 API 端点...\n');

  for (const endpoint of endpoints) {
    try {
      console.log(`测试: ${endpoint}`);
      const response = await makeRequest(endpoint);
      
      if (response.statusCode < 400) {
        console.log(`✅ ${endpoint} - 状态码: ${response.statusCode}`);
      } else {
        console.log(`❌ ${endpoint} - 状态码: ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 错误: ${error.message}`);
    }
  }

  console.log('\n🎉 测试完成！');
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await makeRequest('/');
    console.log('✅ 服务器正在运行');
    return true;
  } catch (error) {
    console.log('❌ 服务器未运行，请先启动开发服务器:');
    console.log('   npm run dev');
    return false;
  }
}

async function main() {
  console.log('GitHub Star 管理器 - API 测试\n');
  
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testEndpoints();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
