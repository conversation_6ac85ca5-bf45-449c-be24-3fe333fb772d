#!/usr/bin/env node

/**
 * GitHub OAuth 诊断脚本
 * 测试 GitHub OAuth 应用配置是否正确
 */

const https = require('https');
const { URLSearchParams } = require('url');

// 从环境变量读取配置
require('dotenv').config({ path: '.env.local' });

const CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;

console.log('🔍 GitHub OAuth 诊断工具\n');

// 检查环境变量
console.log('1. 检查环境变量:');
console.log(`   CLIENT_ID: ${CLIENT_ID ? '✅ 已设置' : '❌ 未设置'}`);
console.log(`   CLIENT_SECRET: ${CLIENT_SECRET ? '✅ 已设置' : '❌ 未设置'}`);

if (!CLIENT_ID || !CLIENT_SECRET) {
  console.log('\n❌ 请确保在 .env.local 中设置了 GITHUB_CLIENT_ID 和 GITHUB_CLIENT_SECRET');
  process.exit(1);
}

// 测试 GitHub API 连接
console.log('\n2. 测试 GitHub API 连接:');

function testGitHubAPI() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.github.com',
      port: 443,
      path: '/user',
      method: 'GET',
      headers: {
        'User-Agent': 'GitHub-Star-Manager-Test',
        'Accept': 'application/vnd.github.v3+json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function main() {
  try {
    const response = await testGitHubAPI();
    if (response.statusCode === 401) {
      console.log('   ✅ GitHub API 连接正常 (401 Unauthorized 是预期的)');
    } else {
      console.log(`   ⚠️  GitHub API 返回状态码: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ GitHub API 连接失败: ${error.message}`);
    console.log('   这可能是网络问题或防火墙阻止了连接');
  }

  console.log('\n3. OAuth 应用配置检查:');
  console.log('   请确保您的 GitHub OAuth 应用配置如下:');
  console.log('   - Application name: GitHub Star Manager');
  console.log('   - Homepage URL: http://localhost:3000');
  console.log('   - Authorization callback URL: http://localhost:3000/api/auth/callback/github');
  
  console.log('\n4. 建议的解决方案:');
  console.log('   如果登录仍然失败，请尝试:');
  console.log('   - 检查网络连接');
  console.log('   - 确认 GitHub OAuth 应用的回调 URL 正确');
  console.log('   - 重新生成 GitHub OAuth 应用的 Client Secret');
  console.log('   - 检查是否有防火墙或代理阻止连接');
}

main().catch(console.error);
