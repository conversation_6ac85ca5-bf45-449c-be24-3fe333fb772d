{"name": "star", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:api": "node scripts/test-api.js", "test:oauth": "node scripts/test-github-oauth.js", "type-check": "tsc --noEmit"}, "dependencies": {"@octokit/rest": "^22.0.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "fuse.js": "^7.1.0", "lucide-react": "^0.523.0", "next": "15.3.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}